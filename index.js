import { extension_settings, getContext } from "../../../extensions.js";
import { saveSettingsDebounced } from "../../../../script.js";
import { statsTracker } from "./statsTracker.js";
import { initTasks } from "./scheduledTasks.js";
import { initScriptAssistant } from "./scriptAssistant.js";
import { initMessagePreview } from "./message-preview.js";

const EXT_ID = "LittleWhiteBox";
const EXT_NAME = "小白X";
const MODULE_NAME = "xiaobaix-memory";
const extensionFolderPath = `scripts/extensions/third-party/${EXT_ID}`;

extension_settings[EXT_ID] = extension_settings[EXT_ID] || {
    enabled: true,
    sandboxMode: false,
    memoryEnabled: true,
    memoryInjectEnabled: true,
    memoryInjectDepth: 2
};

const settings = extension_settings[EXT_ID];

// 全局启用/禁用状态控制
let isXiaobaixEnabled = true;
let moduleInstances = {
    statsTracker: null,
    tasks: null,
    scriptAssistant: null,
    messagePreview: null
};

// 保存原始设置状态
let savedSettings = {
    sandboxMode: null,
    memoryEnabled: null,
    memoryInjectEnabled: null,
    memoryInjectDepth: null,
    recordedEnabled: null,
    previewEnabled: null,
    scriptAssistantEnabled: null,
    scheduledTasksEnabled: null
};

// 导出全局状态供其他模块使用
window.isXiaobaixEnabled = isXiaobaixEnabled;

async function waitForElement(selector, root = document, timeout = 10000) {
    const start = Date.now();
    while (Date.now() - start < timeout) {
        const element = root.querySelector(selector);
        if (element) return element;
        await new Promise(r => setTimeout(r, 100));
    }
    return null;
}

function generateUniqueId() {
    return `xiaobaix-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
}

function shouldRenderContent(content, className) {
    if (!content || typeof content !== 'string') return false;
    
    const containsHtmlTags = (
        content.includes('<html') || 
        content.includes('<!DOCTYPE') || 
        content.includes('<body') || 
        content.includes('<head') || 
        content.includes('<script') || 
        content.includes('<div') || 
        content.includes('<style')
    );    
    
    return containsHtmlTags;
}

function createIframeApi() {
    return `
    window.STBridge = {
        sendMessageToST: function(type, data = {}) {
            try {
                window.parent.postMessage({
                    source: 'xiaobaix-iframe',
                    type: type, 
                    ...data
                }, '*');
            } catch(e) {}
        },
        
        updateHeight: function() {
            try {
                const height = document.body.scrollHeight;
                if (height > 0) {
                    this.sendMessageToST('resize', { height });
                }
            } catch(e) {}
        }
    };
    
    window.STscript = async function(command) {
        return new Promise((resolve, reject) => {
            try {
                const id = Date.now().toString() + Math.random().toString(36).substring(2);
                
                window.STBridge.sendMessageToST('runCommand', { command, id });
                
                const listener = function(event) {
                    if (!event.data || event.data.source !== 'xiaobaix-host') return;
                    
                    const data = event.data;
                    if ((data.type === 'commandResult' || data.type === 'commandError') && data.id === id) {
                        window.removeEventListener('message', listener);
                        
                        if (data.type === 'commandResult') {
                            resolve(data.result);
                        } else {
                            reject(new Error(data.error));
                        }
                    }
                };
                
                window.addEventListener('message', listener);
                
                setTimeout(() => {
                    window.removeEventListener('message', listener);
                    reject(new Error('Command timeout'));
                }, 30000);
            } catch(e) {
                reject(e);
            }
        });
    };
    
    function setupAutoResize() {
        window.STBridge.updateHeight();
        
        window.addEventListener('resize', () => window.STBridge.updateHeight());
        window.addEventListener('load', () => window.STBridge.updateHeight());
        
        try {
            const observer = new MutationObserver(() => window.STBridge.updateHeight());
            observer.observe(document.body, {
                attributes: true,
                childList: true,
                subtree: true,
                characterData: true
            });
        } catch(e) {}
        
        setInterval(() => window.STBridge.updateHeight(), 1000);
        
        window.addEventListener('load', function() {
            Array.from(document.images).forEach(img => {
                if (!img.complete) {
                    img.addEventListener('load', () => window.STBridge.updateHeight());
                    img.addEventListener('error', () => window.STBridge.updateHeight());
                }
            });
        });
    }
    
    function setupSecurity() {
        document.addEventListener('click', function(e) {
            const link = e.target.closest('a');
            if (link && link.href && link.href.startsWith('http')) {
                if (link.target !== '_blank') {
                    e.preventDefault();
                    window.open(link.href, '_blank');
                }
            }
        });
    }
    
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            setupAutoResize();
            setupSecurity();
        });
    } else {
        setupAutoResize();
        setupSecurity();
    }
    `;
}

async function executeSlashCommand(command) {
    try {
        if (!command) return { error: "命令为空" };
        
        if (!command.startsWith('/')) {
            command = '/' + command;
        }
        
        const { executeSlashCommands, substituteParams } = getContext();
        
        if (typeof executeSlashCommands !== 'function') {
            throw new Error("executeSlashCommands 函数不可用");
        }
        
        command = substituteParams(command);
        
        const result = await executeSlashCommands(command, true);
        
        if (result && typeof result === 'object' && result.pipe !== undefined) {
            const pipeValue = result.pipe;
            
            if (typeof pipeValue === 'string') {
                try {
                    return JSON.parse(pipeValue);
                } catch {
                    return pipeValue;
                }
            }
            
            return pipeValue;
        }
        
        if (typeof result === 'string' && result.trim()) {
            try {
                return JSON.parse(result);
            } catch {
                return result;
            }
        }
        
        return result === undefined ? "" : result;
        
    } catch (err) {
        throw err;
    }
}

function handleIframeMessage(event) {
    if (!event.data || event.data.source !== 'xiaobaix-iframe') return;
    
    const data = event.data;
    
    switch (data.type) {
        case 'resize':
            handleResizeMessage(event.source, data);
            break;
        case 'runCommand':
            handleCommandMessage(event.source, data);
            break;
    }
}

function handleResizeMessage(source, data) {
    try {
        const iframes = document.querySelectorAll('iframe.xiaobaix-iframe');
        for (const iframe of iframes) {
            if (iframe.contentWindow === source) {
                iframe.style.height = `${data.height}px`;
                break;
            }
        }
    } catch (err) {}
}

async function handleCommandMessage(source, data) {
    try {
        const result = await executeSlashCommand(data.command);
        
        source.postMessage({
            source: 'xiaobaix-host',
            type: 'commandResult',
            id: data.id,
            result: result
        }, '*');
    } catch (err) {
        source.postMessage({
            source: 'xiaobaix-host',
            type: 'commandError',
            id: data.id,
            error: err.message || String(err)
        }, '*');
    }
}

function renderHtmlInIframe(htmlContent, container, codeBlock) {
    try {
        const iframeId = generateUniqueId();
        
        const iframe = document.createElement('iframe');
        iframe.id = iframeId;
        iframe.className = 'xiaobaix-iframe';
        iframe.style.cssText = `
            width: 100%;
            border: none;
            background: transparent;
            overflow: hidden;
            height: 0;
            margin: 0;
            padding: 0;
            display: block;
        `;
        iframe.setAttribute('frameborder', '0');
        iframe.setAttribute('scrolling', 'no');
        
        if (settings.sandboxMode) {
            iframe.setAttribute('sandbox', 'allow-scripts allow-same-origin allow-popups allow-forms');
        }
        
        container.appendChild(iframe);
        
        let finalHtml = prepareHtmlContent(htmlContent);
        
        const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
        iframeDoc.open();
        iframeDoc.write(finalHtml);
        iframeDoc.close();
        
        if (codeBlock) {
            codeBlock.style.display = 'none';
        }
        
        return iframe;
    } catch (err) {
        return null;
    }
}

function prepareHtmlContent(htmlContent) {
    if (htmlContent.includes('<html') && htmlContent.includes('</html>')) {
        return htmlContent.replace('</head>', `<script>${createIframeApi()}</script></head>`);
    }
    
    if (htmlContent.includes('<body') && htmlContent.includes('</body>')) {
        return `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            margin: 0;
            padding: 10px;
            font-family: inherit;
            color: inherit;
            background: transparent;
        }
    </style>
    <script>${createIframeApi()}</script>
</head>
${htmlContent}
</html>`;
    }
    
    return `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            margin: 0;
            padding: 10px;
            font-family: inherit;
            color: inherit;
            background: transparent;
        }
    </style>
    <script>${createIframeApi()}</script>
</head>
<body>
${htmlContent}
</body>
</html>`;
}

// 保存当前设置状态
function saveCurrentSettings() {
    try {
        savedSettings.sandboxMode = settings.sandboxMode;
        savedSettings.memoryEnabled = settings.memoryEnabled;
        savedSettings.memoryInjectEnabled = settings.memoryInjectEnabled;
        savedSettings.memoryInjectDepth = settings.memoryInjectDepth;

        // 保存其他模块的设置
        savedSettings.recordedEnabled = extension_settings[EXT_ID].messagePreview?.recorded?.enabled;
        savedSettings.previewEnabled = extension_settings[EXT_ID].messagePreview?.preview?.enabled;
        savedSettings.scriptAssistantEnabled = extension_settings[EXT_ID].scriptAssistant?.enabled;
        savedSettings.scheduledTasksEnabled = extension_settings[EXT_ID].tasks?.enabled;

        console.log('[小白X] 设置已保存');
    } catch (err) {
        console.error('[小白X] 保存设置时出错:', err);
    }
}

// 恢复保存的设置状态
function restoreSettings() {
    try {
        if (savedSettings.sandboxMode !== null) {
            settings.sandboxMode = savedSettings.sandboxMode;
            $("#xiaobaix_sandbox").prop("checked", savedSettings.sandboxMode);
        }

        if (savedSettings.memoryEnabled !== null) {
            settings.memoryEnabled = savedSettings.memoryEnabled;
            $("#xiaobaix_memory_enabled").prop("checked", savedSettings.memoryEnabled);
        }

        if (savedSettings.memoryInjectEnabled !== null) {
            settings.memoryInjectEnabled = savedSettings.memoryInjectEnabled;
            $("#xiaobaix_memory_inject").prop("checked", savedSettings.memoryInjectEnabled);
        }

        if (savedSettings.memoryInjectDepth !== null) {
            settings.memoryInjectDepth = savedSettings.memoryInjectDepth;
            $("#xiaobaix_memory_depth").val(savedSettings.memoryInjectDepth);
        }

        // 恢复其他模块设置
        if (savedSettings.recordedEnabled !== null) {
            if (!extension_settings[EXT_ID].messagePreview) extension_settings[EXT_ID].messagePreview = {};
            if (!extension_settings[EXT_ID].messagePreview.recorded) extension_settings[EXT_ID].messagePreview.recorded = {};
            extension_settings[EXT_ID].messagePreview.recorded.enabled = savedSettings.recordedEnabled;
            $("#xiaobaix_recorded_enabled").prop("checked", savedSettings.recordedEnabled);
        }

        if (savedSettings.previewEnabled !== null) {
            if (!extension_settings[EXT_ID].messagePreview) extension_settings[EXT_ID].messagePreview = {};
            if (!extension_settings[EXT_ID].messagePreview.preview) extension_settings[EXT_ID].messagePreview.preview = {};
            extension_settings[EXT_ID].messagePreview.preview.enabled = savedSettings.previewEnabled;
            $("#xiaobaix_preview_enabled").prop("checked", savedSettings.previewEnabled);
        }

        if (savedSettings.scriptAssistantEnabled !== null) {
            if (!extension_settings[EXT_ID].scriptAssistant) extension_settings[EXT_ID].scriptAssistant = {};
            extension_settings[EXT_ID].scriptAssistant.enabled = savedSettings.scriptAssistantEnabled;
            $("#xiaobaix_script_assistant").prop("checked", savedSettings.scriptAssistantEnabled);
        }

        if (savedSettings.scheduledTasksEnabled !== null) {
            if (!extension_settings[EXT_ID].tasks) extension_settings[EXT_ID].tasks = {};
            extension_settings[EXT_ID].tasks.enabled = savedSettings.scheduledTasksEnabled;
            $("#scheduled_tasks_enabled").prop("checked", savedSettings.scheduledTasksEnabled);
        }

        console.log('[小白X] 设置已恢复');
    } catch (err) {
        console.error('[小白X] 恢复设置时出错:', err);
    }
}

// 禁用所有设置控件
function disableAllSettingsControls() {
    try {
        const controlSelectors = [
            "#xiaobaix_sandbox",
            "#xiaobaix_memory_enabled",
            "#xiaobaix_memory_inject",
            "#xiaobaix_memory_depth",
            "#xiaobaix_recorded_enabled",
            "#xiaobaix_preview_enabled",
            "#xiaobaix_script_assistant",
            "#scheduled_tasks_enabled"
        ];

        controlSelectors.forEach(selector => {
            const element = $(selector);
            element.prop("disabled", true).addClass("disabled-control");
        });

        // 添加CSS样式使控件变暗
        if (!document.getElementById('xiaobaix-disabled-style')) {
            const style = document.createElement('style');
            style.id = 'xiaobaix-disabled-style';
            style.textContent = `
                .disabled-control {
                    opacity: 0.4 !important;
                    pointer-events: none !important;
                    cursor: not-allowed !important;
                }
                .disabled-control + label {
                    opacity: 0.4 !important;
                    cursor: not-allowed !important;
                }
            `;
            document.head.appendChild(style);
        }
    } catch (err) {
        console.error('[小白X] 禁用控件时出错:', err);
    }
}

// 启用所有设置控件
function enableAllSettingsControls() {
    try {
        const controlSelectors = [
            "#xiaobaix_sandbox",
            "#xiaobaix_memory_enabled",
            "#xiaobaix_memory_inject",
            "#xiaobaix_memory_depth",
            "#xiaobaix_recorded_enabled",
            "#xiaobaix_preview_enabled",
            "#xiaobaix_script_assistant",
            "#scheduled_tasks_enabled"
        ];

        controlSelectors.forEach(selector => {
            const element = $(selector);
            element.prop("disabled", false).removeClass("disabled-control");
        });

        // 移除禁用样式
        const style = document.getElementById('xiaobaix-disabled-style');
        if (style) {
            style.remove();
        }
    } catch (err) {
        console.error('[小白X] 启用控件时出错:', err);
    }
}

// 禁用所有小白X功能
function disableAllFeatures() {
    try {
        // 保存当前设置
        saveCurrentSettings();

        // 将所有子功能设置为false
        settings.sandboxMode = false;
        settings.memoryEnabled = false;
        settings.memoryInjectEnabled = false;

        // 更新UI显示
        $("#xiaobaix_sandbox").prop("checked", false);
        $("#xiaobaix_memory_enabled").prop("checked", false);
        $("#xiaobaix_memory_inject").prop("checked", false);
        $("#xiaobaix_recorded_enabled").prop("checked", false);
        $("#xiaobaix_preview_enabled").prop("checked", false);
        $("#xiaobaix_script_assistant").prop("checked", false);
        $("#scheduled_tasks_enabled").prop("checked", false);

        // 禁用所有控件
        disableAllSettingsControls();

        // 移除所有iframe
        document.querySelectorAll('iframe.xiaobaix-iframe').forEach(iframe => {
            iframe.remove();
        });

        // 恢复被隐藏的代码块
        document.querySelectorAll('pre[data-xiaobaix-bound="true"]').forEach(pre => {
            pre.style.display = '';
            delete pre.dataset.xiaobaixBound;
        });

        // 移除内存按钮
        document.querySelectorAll('.memory-button').forEach(btn => btn.remove());

        // 隐藏预览按钮
        document.querySelectorAll('#message_preview_btn').forEach(btn => btn.style.display = 'none');

        // 移除历史记录按钮
        document.querySelectorAll('.mes_history_preview').forEach(btn => btn.remove());

        // 清理统计数据提示词
        if (moduleInstances.statsTracker && typeof moduleInstances.statsTracker.removeMemoryPrompt === 'function') {
            moduleInstances.statsTracker.removeMemoryPrompt();
        }

        // 清理脚本助手提示词
        if (window.removeScriptDocs && typeof window.removeScriptDocs === 'function') {
            window.removeScriptDocs();
        }

        console.log('[小白X] 所有功能已禁用');
    } catch (err) {
        console.error('[小白X] 禁用功能时出错:', err);
    }
}

// 启用所有小白X功能
function enableAllFeatures() {
    try {
        // 启用所有控件
        enableAllSettingsControls();

        // 恢复保存的设置
        restoreSettings();

        // 保存设置到文件
        saveSettingsDebounced();

        // 重新处理现有消息
        setTimeout(() => {
            processExistingMessages();
        }, 100);

        // 重新初始化统计功能
        if (settings.memoryEnabled && moduleInstances.statsTracker) {
            setTimeout(() => {
                if (typeof moduleInstances.statsTracker.updateMemoryPrompt === 'function') {
                    moduleInstances.statsTracker.updateMemoryPrompt();
                }
            }, 200);
        }

        // 重新初始化脚本助手
        if (extension_settings[EXT_ID].scriptAssistant?.enabled && window.injectScriptDocs) {
            setTimeout(() => {
                if (typeof window.injectScriptDocs === 'function') {
                    window.injectScriptDocs();
                }
            }, 300);
        }

        // 重新显示预览按钮
        const previewSettings = extension_settings[EXT_ID].messagePreview;
        if (previewSettings?.preview?.enabled) {
            setTimeout(() => {
                document.querySelectorAll('#message_preview_btn').forEach(btn => btn.style.display = '');
            }, 400);
        }

        console.log('[小白X] 所有功能已启用');
    } catch (err) {
        console.error('[小白X] 启用功能时出错:', err);
    }
}

function processCodeBlocks(messageElement) {
    if (!settings.enabled || !isXiaobaixEnabled) return;

    try {
        const codeBlocks = messageElement.querySelectorAll('pre > code');

        codeBlocks.forEach(codeBlock => {
            const preElement = codeBlock.parentElement;

            if (preElement.dataset.xiaobaixBound === 'true') return;
            preElement.dataset.xiaobaixBound = 'true';

            const codeContent = codeBlock.textContent || '';
            const codeClass = codeBlock.className || '';

            if (shouldRenderContent(codeContent, codeClass)) {
                renderHtmlInIframe(codeContent, preElement.parentNode, preElement);
            }
        });
    } catch (err) {}
}

async function setupSettings() {
    try {
        const settingsContainer = await waitForElement("#extensions_settings");
        if (!settingsContainer) return;
        
        const response = await fetch(`${extensionFolderPath}/settings.html`);
        const settingsHtml = await response.text();
        
        $(settingsContainer).append(settingsHtml);

        $("#xiaobaix_enabled").prop("checked", settings.enabled).on("change", function() {
            const wasEnabled = settings.enabled;
            settings.enabled = !!$(this).prop("checked");
            isXiaobaixEnabled = settings.enabled;
            window.isXiaobaixEnabled = isXiaobaixEnabled; // 更新全局变量
            saveSettingsDebounced();

            // 立即启用或禁用功能
            if (settings.enabled && !wasEnabled) {
                enableAllFeatures();
            } else if (!settings.enabled && wasEnabled) {
                disableAllFeatures();
            }
        });

        // 如果初始状态是禁用的，则禁用所有控件
        if (!settings.enabled) {
            disableAllSettingsControls();
        }
        
        $("#xiaobaix_sandbox").prop("checked", settings.sandboxMode).on("change", function() {
            if (!isXiaobaixEnabled) return; // 总开关关闭时不处理
            settings.sandboxMode = !!$(this).prop("checked");
            saveSettingsDebounced();
        });

        $("#xiaobaix_memory_enabled").prop("checked", settings.memoryEnabled).on("change", function() {
            if (!isXiaobaixEnabled) return; // 总开关关闭时不处理
            settings.memoryEnabled = !!$(this).prop("checked");
            saveSettingsDebounced();

            if (settings.memoryEnabled && settings.memoryInjectEnabled) {
                statsTracker.updateMemoryPrompt();
            } else if (!settings.memoryEnabled) {
                statsTracker.removeMemoryPrompt();
            }
        });

        $("#xiaobaix_memory_inject").prop("checked", settings.memoryInjectEnabled).on("change", function() {
            if (!isXiaobaixEnabled) return; // 总开关关闭时不处理
            settings.memoryInjectEnabled = !!$(this).prop("checked");
            saveSettingsDebounced();

            if (settings.memoryEnabled && settings.memoryInjectEnabled) {
                statsTracker.updateMemoryPrompt();
            } else {
                statsTracker.removeMemoryPrompt();
            }
        });

        $("#xiaobaix_memory_depth").val(settings.memoryInjectDepth).on("change", function() {
            if (!isXiaobaixEnabled) return; // 总开关关闭时不处理
            settings.memoryInjectDepth = parseInt($(this).val()) || 2;
            saveSettingsDebounced();

            if (settings.memoryEnabled && settings.memoryInjectEnabled) {
                statsTracker.updateMemoryPrompt();
            }
        });
    } catch (err) {}
}

function setupMenuTabs() {
    console.log('Setting up menu tabs...');

    $(document).on('click', '.menu-tab', function() {
        console.log('Menu tab clicked:', $(this).attr('data-target'));
        const targetId = $(this).attr('data-target');

        $('.menu-tab').removeClass('active');

        $('.settings-section').hide();

        $(this).addClass('active');

        $('.' + targetId).show();
    });

    setTimeout(() => {
        console.log('Setting default tab state...');
        const jsMemorySection = $('.js-memory');
        const taskSection = $('.task');
        const instructionsSection = $('.instructions');
        const jsMemoryTab = $('.menu-tab[data-target="js-memory"]');
        const taskTab = $('.menu-tab[data-target="task"]');
        const instructionsTab = $('.menu-tab[data-target="instructions"]');

        console.log('Found elements:', {
            jsMemorySection: jsMemorySection.length,
            taskSection: taskSection.length,
            instructionsSection: instructionsSection.length,
            jsMemoryTab: jsMemoryTab.length,
            taskTab: taskTab.length,
            instructionsTab: instructionsTab.length
        });

        if (jsMemorySection.length && taskSection.length && instructionsSection.length) {
            jsMemorySection.show();
            taskSection.hide();
            instructionsSection.hide();

            jsMemoryTab.addClass('active');
            taskTab.removeClass('active');
            instructionsTab.removeClass('active');
            console.log('Default state set successfully');
        } else {
            console.log('Some elements not found, retrying...');

            setTimeout(() => {
                $('.js-memory').show();
                $('.task').hide();
                $('.instructions').hide();
                $('.menu-tab[data-target="js-memory"]').addClass('active');
                $('.menu-tab[data-target="task"]').removeClass('active');
                $('.menu-tab[data-target="instructions"]').removeClass('active');
            }, 500);
        }
    }, 300);
}

function setupEventListeners() {
    const { eventSource, event_types } = getContext();
    
    eventSource.on(event_types.MESSAGE_RECEIVED, onMessageComplete);
    eventSource.on(event_types.USER_MESSAGE_RENDERED, onMessageRendered);
    eventSource.on(event_types.CHARACTER_MESSAGE_RENDERED, onMessageRendered);
    eventSource.on(event_types.CHAT_CHANGED, onChatChanged);
    
    window.addEventListener('message', handleIframeMessage);
    
    async function onMessageComplete(data) {
        if (!settings.enabled || !settings.memoryEnabled || !isXiaobaixEnabled) return;

        setTimeout(async () => {
            const messageId = typeof data === 'object' ? data.messageId : data;

            if (!messageId) return;

            const messageElement = document.querySelector(`div.mes[mesid="${messageId}"] .mes_text`);
            if (messageElement) {
                const messageText = messageElement.textContent || '';
                const characterName = statsTracker.getCharacterFromMessage(messageElement);
                await statsTracker.updateStatisticsForNewMessage(messageText, characterName);

                const memoryButton = $(`.mes[mesid="${messageId}"] .memory-button`);
                if (memoryButton.length) {
                    memoryButton.addClass('has-memory');
                }
            }
        }, 300);
    }
    
    async function onMessageRendered(data) {
        if (!settings.enabled || !isXiaobaixEnabled) return;

        setTimeout(async () => {
            const messageId = data.messageId;
            const messageElement = document.querySelector(`div.mes[mesid="${messageId}"] .mes_text`);
            if (messageElement) {
                processCodeBlocks(messageElement);

                if (settings.memoryEnabled) {
                    statsTracker.addMemoryButtonToMessage(messageId);
                }
            }
        }, 100);
    }
    
    async function onChatChanged() {
        if (!settings.memoryEnabled || !isXiaobaixEnabled) return;

        try {
            setTimeout(async () => {
                let stats = await executeSlashCommand('/getvar xiaobaix_stats');

                if (!stats || stats === "undefined") {
                    const messages = await statsTracker.processMessageHistory();
                    if (messages && messages.length > 0) {
                        const newStats = statsTracker.createEmptyStats();

                        for (const message of messages) {
                            statsTracker.updateStatsFromText(newStats, message.content, message.name);
                        }

                        await executeSlashCommand(`/setvar key=xiaobaix_stats ${JSON.stringify(newStats)}`);

                        if (settings.memoryInjectEnabled) {
                            statsTracker.updateMemoryPrompt();
                        }
                    }
                } else if (settings.memoryInjectEnabled) {
                    statsTracker.updateMemoryPrompt();
                }
            }, 500);
        } catch (error) {}
    }
}

function processExistingMessages() {
    if (!settings.enabled || !isXiaobaixEnabled) return;

    const messages = document.querySelectorAll('.mes_text');
    messages.forEach(message => {
        processCodeBlocks(message);
    });

    if (settings.memoryEnabled) {
        $('#chat .mes').each(function() {
            const messageId = $(this).attr('mesid');
            if (messageId) {
                statsTracker.addMemoryButtonToMessage(messageId);
            }
        });
    }
}

async function initExtension() {
    try {
        // 初始化全局状态
        isXiaobaixEnabled = settings.enabled;
        window.isXiaobaixEnabled = isXiaobaixEnabled;

        const response = await fetch(`${extensionFolderPath}/style.css`);
        const styleText = await response.text();

        const styleElement = document.createElement('style');
        styleElement.textContent = styleText;
        document.head.appendChild(styleElement);

        // 保存模块实例引用
        moduleInstances.statsTracker = statsTracker;
        statsTracker.init(EXT_ID, MODULE_NAME, settings, executeSlashCommand);

        await setupSettings();
        setupEventListeners();
        initTasks();
        initScriptAssistant();

        setTimeout(() => {
            setupMenuTabs();
        }, 500);
        
        setTimeout(async () => {
            if (isXiaobaixEnabled) {
                processExistingMessages();

                if (settings.memoryEnabled) {
                    const messages = await statsTracker.processMessageHistory();
                    if (messages && messages.length > 0) {
                        const stats = statsTracker.createEmptyStats();

                        for (const message of messages) {
                            statsTracker.updateStatsFromText(stats, message.content, message.name);
                        }

                        await executeSlashCommand(`/setvar key=xiaobaix_stats ${JSON.stringify(stats)}`);

                        if (settings.memoryInjectEnabled) {
                            statsTracker.updateMemoryPrompt();
                        }
                    }
                }
            }
        }, 1000);

        setTimeout(() => {
            initMessagePreview();
        }, 1500);

        setInterval(() => {
            if (isXiaobaixEnabled) {
                processExistingMessages();
            }
        }, 5000);
    } catch (err) {
        console.error('[小白X] 初始化出错:', err);
    }
}


export { executeSlashCommand };

initExtension();
